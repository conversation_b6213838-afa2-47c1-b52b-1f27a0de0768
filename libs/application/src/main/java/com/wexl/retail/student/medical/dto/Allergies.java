package com.wexl.retail.student.medical.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class Allergies {
  private Boolean bronchialAsthma;
  private Boolean headaches;
  private Boolean vomitings;
  private Boolean urticaria;
  private Boolean convulsions;
  private Boolean tonsillitis;
  private Boolean anaemia;
  private Boolean sinusitis;
  private Boolean bronchitis;
  private Boolean tuberculosis;
  private Boolean pneumonia;
  private Boolean epilepsyAnyOther;
  private String anyOther;
}
