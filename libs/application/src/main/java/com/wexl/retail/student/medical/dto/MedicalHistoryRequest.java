package com.wexl.retail.student.medical.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDate;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MedicalHistoryRequest {

  @JsonProperty("date_of_birth")
  private LocalDate dateOfBirth;

  @JsonProperty("blood_group")
  private String bloodGroup;

  @JsonProperty("height_cm")
  private String height;

  @JsonProperty("weight_kg")
  private String weight;

  @Valid
  @JsonProperty("allergies")
  private Allergies allergies;

  @Valid
  @JsonProperty("illness")
  private Illness illness;

  @Valid
  @JsonProperty("remarks")
  private Remarks remarks;
}
