package com.wexl.retail.student.medical.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MedicalHistoryResponse {

  @JsonProperty("id")
  private Long id;

  @JsonProperty("student_id")
  private Long studentId;

  @JsonProperty("date_of_birth")
  private LocalDate dateOfBirth;

  @JsonProperty("blood_group")
  private String bloodGroup;

  @JsonProperty("height_cm")
  private String height;

  @JsonProperty("weight_kg")
  private String weight;

  @JsonProperty("allergies")
  private Allergies allergies;

  @JsonProperty("illness")
  private Illness illness;

  @JsonProperty("remarks")
  private Remarks remarks;
}
