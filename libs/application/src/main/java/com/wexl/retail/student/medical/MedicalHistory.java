package com.wexl.retail.student.medical;

import com.wexl.retail.student.medical.dto.Allergies;
import com.wexl.retail.student.medical.dto.Illness;
import com.wexl.retail.student.medical.dto.Remarks;
import io.hypersistence.utils.hibernate.type.json.JsonType;
import jakarta.persistence.*;
import java.time.LocalDate;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Type;

@AllArgsConstructor
@Entity
@Data
@Table(name = "medical_profile_histories")
@NoArgsConstructor
public class MedicalHistory {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  private Long student;
  private LocalDate dateOfBirth;
  private String bloodGroup;
  private String height;
  private String weight;
  private String drugOrMedicines;
  private String foodAllergy;
  private String chronicDiseases;
  private String heartCondition;
  private String surgeryOrAdmittedHospital;
  private String wearSpectacles;
  private String dentalTreatment;

  @Type(JsonType.class)
  @Column(columnDefinition = "jsonignore")
  private Allergies allergies;

  @Type(JsonType.class)
  @Column(columnDefinition = "jsonignore")
  private Illness illness;

  @Type(JsonType.class)
  @Column(columnDefinition = "jsonignore")
  private Remarks remarks;
}
