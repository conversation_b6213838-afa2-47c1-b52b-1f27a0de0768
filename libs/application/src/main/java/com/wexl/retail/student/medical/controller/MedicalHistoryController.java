package com.wexl.retail.student.medical.controller;

import com.wexl.retail.commons.security.annotation.IsOrgAdminOrTeacher;
import com.wexl.retail.commons.security.annotation.IsStudent;
import com.wexl.retail.student.medical.dto.MedicalHistoryRequest;
import com.wexl.retail.student.medical.dto.MedicalHistoryResponse;
import com.wexl.retail.student.medical.service.MedicalHistoryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

@RestController
@RequiredArgsConstructor
@Slf4j
@RequestMapping("/orgs/{orgSlug}/students/{studentId}/medical-history")
public class MedicalHistoryController {

  private final MedicalHistoryService medicalHistoryService;

  /**
   * Get all medical histories for a student
   * Accessible by org admins, teachers, and the student themselves
   */
  @GetMapping
  @IsOrgAdminOrTeacher
  public ResponseEntity<List<MedicalHistoryResponse>> getMedicalHistories(
      @PathVariable String orgSlug,
      @PathVariable Long studentId) {
    
    log.info("Getting medical histories for student: {} in org: {}", studentId, orgSlug);
    
    List<MedicalHistoryResponse> histories = medicalHistoryService.getMedicalHistoriesByStudent(studentId);
    
    return ResponseEntity.ok(histories);
  }

  /**
   * Get medical history by ID for a specific student
   * Accessible by org admins, teachers, and the student themselves
   */
  @GetMapping("/{id}")
  @IsOrgAdminOrTeacher
  public ResponseEntity<MedicalHistoryResponse> getMedicalHistoryById(
      @PathVariable String orgSlug,
      @PathVariable Long studentId,
      @PathVariable Long id) {
    
    log.info("Getting medical history with ID: {} for student: {} in org: {}", id, studentId, orgSlug);
    
    MedicalHistoryResponse history = medicalHistoryService.getMedicalHistoryById(studentId, id);
    
    return ResponseEntity.ok(history);
  }

  /**
   * Create new medical history for a student
   * Only accessible by org admins and teachers
   */
  @PostMapping
  @IsOrgAdminOrTeacher
  public ResponseEntity<MedicalHistoryResponse> createMedicalHistory(
      @PathVariable String orgSlug,
      @PathVariable Long studentId,
      @Valid @RequestBody MedicalHistoryRequest request) {
    
    log.info("Creating medical history for student: {} in org: {}", studentId, orgSlug);
    
    MedicalHistoryResponse history = medicalHistoryService.createMedicalHistory(studentId, request);
    
    return ResponseEntity.status(HttpStatus.CREATED).body(history);
  }

  /**
   * Update existing medical history
   * Only accessible by org admins and teachers
   */
  @PutMapping("/{id}")
  @IsOrgAdminOrTeacher
  public ResponseEntity<MedicalHistoryResponse> updateMedicalHistory(
      @PathVariable String orgSlug,
      @PathVariable Long studentId,
      @PathVariable Long id,
      @Valid @RequestBody MedicalHistoryRequest request) {
    
    log.info("Updating medical history with ID: {} for student: {} in org: {}", id, studentId, orgSlug);
    
    MedicalHistoryResponse history = medicalHistoryService.updateMedicalHistory(studentId, id, request);
    
    return ResponseEntity.ok(history);
  }

  /**
   * Delete medical history
   * Only accessible by org admins and teachers
   */
  @DeleteMapping("/{id}")
  @IsOrgAdminOrTeacher
  public ResponseEntity<Void> deleteMedicalHistory(
      @PathVariable String orgSlug,
      @PathVariable Long studentId,
      @PathVariable Long id) {
    
    log.info("Deleting medical history with ID: {} for student: {} in org: {}", id, studentId, orgSlug);
    
    medicalHistoryService.deleteMedicalHistory(studentId, id);
    
    return ResponseEntity.noContent().build();
  }

  /**
   * Get medical histories for the authenticated student
   * Only accessible by the student themselves
   */
  @GetMapping("/my-medical-history")
  @IsStudent
  public ResponseEntity<List<MedicalHistoryResponse>> getMyMedicalHistories(
      @PathVariable String orgSlug,
      @PathVariable Long studentId) {
    
    log.info("Student {} getting their own medical histories in org: {}", studentId, orgSlug);
    
    List<MedicalHistoryResponse> histories = medicalHistoryService.getMedicalHistoriesByStudent(studentId);
    
    return ResponseEntity.ok(histories);
  }
}
