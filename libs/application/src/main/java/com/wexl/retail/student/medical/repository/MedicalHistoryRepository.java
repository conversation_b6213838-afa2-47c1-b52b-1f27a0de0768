package com.wexl.retail.student.medical.repository;

import com.wexl.retail.student.medical.MedicalHistory;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@EnableJpaRepositories
@Repository
public interface MedicalHistoryRepository extends JpaRepository<MedicalHistory, Long> {
  
  /**
   * Find medical history by student ID
   */
  List<MedicalHistory> findByStudent(Long studentId);
  
  /**
   * Find medical history by student ID and ID
   */
  Optional<MedicalHistory> findByStudentAndId(Long studentId, Long id);
  
  /**
   * Check if medical history exists for a student
   */
  boolean existsByStudent(Long studentId);
  
  /**
   * Delete medical history by student ID and ID
   */
  void deleteByStudentAndId(Long studentId, Long id);
}
