package com.wexl.retail.student.medical.controller;


import com.wexl.retail.student.medical.MedicalHistory;
import com.wexl.retail.student.medical.dto.MedicalProfileRequest;
import com.wexl.retail.student.medical.service.MedicalHistoryService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@RequiredArgsConstructor
@RequestMapping("/orgs/{orgSlug}")
public class MedicalController {

    private final MedicalHistoryService medicalHistoryService;

    @PostMapping("/students/{studentId}/medical-history")
    public void createMedicalHistory(
        @PathVariable String orgSlug,
        @PathVariable Long studentId,
        @RequestBody MedicalProfileRequest request) {
        medicalHistoryService.createMedicalHistory(studentId, request);
    }
}
