package com.wexl.retail.student.medical.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.guardian.dto.GuardianRequest;
import lombok.Builder;

public record MedicalProfileRequest() {
  @Builder
  public record request(
      @JsonProperty("student_id") Long studentId,
      @JsonProperty("student_name") String studentName,
      @JsonProperty("blood_group") String bloodGroup,
      @JsonProperty("height_cm") String height,
      @JsonProperty("weight_kg") String weight,
      @JsonProperty("guardians") GuardianRequest guardians,
      @JsonProperty("drug/medicines") String drugMedicines,
      @JsonProperty("food allergy") String foodAllergy,
      @JsonProperty("allergies") Allergies allergies,
      @JsonProperty("illness") Illness illness,
      @JsonProperty("Chronic diseases") String chronicDiseases,
      @JsonProperty("Heart Condition") String heartCondition,
      
      @<PERSON>sonProperty("remarks") Remarks remarks) {}
}
